# AutoCorner Website - Comprehensive Translations Checklist

## Overview
This document catalogs all hardcoded text strings found in the frontend that need to be moved to Sanity CMS for proper internationalization support, following the established dictionary system architecture.

## Current Translation Status
✅ **Sanity Studio**: Fully translated (EN/FR) with comprehensive dictionary system
❌ **Frontend Components**: Contains extensive hardcoded strings that need to be moved to Sanity
❌ **Global Strings**: Need centralized management in new Sanity singleton

---

## 1. BRANDS PAGE SINGLETON UPDATES

### 1.1 Update Brands Page Singleton (`src/sanity/schemas/singletons/pages/brands-page.ts`)
**Current Status**: Basic page structure exists, needs UI content fields
**Following**: `doc/llm/sanity/translations/overview.md` dictionary system

#### Add new field groups and objects:

**Group 1: Brand Actions** (`brandActions`)
```typescript
// Create object: src/sanity/schemas/objects/brand-actions.ts
export default defineType({
  name: "brandActions",
  title: "Brand Actions",
  type: "object",
  fields: [
    { name: "bookTestDrive", title: "Book Test Drive", type: "string" },
    { name: "findDealer", title: "Find Dealer", type: "string" },
    { name: "contactSales", title: "Contact Sales", type: "string" },
    { name: "getBrochure", title: "Get Brochure", type: "string" },
    { name: "experienceVehicle", title: "Experience the vehicle", type: "string" },
    { name: "locateNearestDealer", title: "Locate nearest dealer", type: "string" },
    { name: "speakWithExpert", title: "Speak with expert", type: "string" },
    { name: "downloadMaterials", title: "Download materials", type: "string" }
  ]
})

// Add to brands-page.ts
defineField({
  name: "brandActions",
  title: "Brand Actions",
  type: "brandActions",
  group: "brandSettings"
})
```

**Group 2: Vehicle Model Navigation** (`vehicleModelNavigation`)
```typescript
// Create object: src/sanity/schemas/objects/vehicle-model-navigation.ts
export default defineType({
  name: "vehicleModelNavigation",
  title: "Vehicle Model Navigation",
  type: "object",
  fields: [
    // Navigation Items
    { name: "overview", title: "Overview", type: "string" },
    { name: "gallery", title: "Gallery", type: "string" },
    { name: "configure", title: "Configure", type: "string" },
    { name: "compare", title: "Compare", type: "string" },

    // Navigation Descriptions
    { name: "vehicleDetails", title: "Vehicle details", type: "string" },
    { name: "photosAndVideos", title: "Photos & videos", type: "string" },
    { name: "buildAndPrice", title: "Build & price", type: "string" },
    { name: "vsCompetitors", title: "VS competitors", type: "string" }
  ]
})
```

**Group 3: Vehicle Model Tabs** (`vehicleModelTabs`)
```typescript
// Create object: src/sanity/schemas/objects/vehicle-model-tabs.ts
export default defineType({
  name: "vehicleModelTabs",
  title: "Vehicle Model Tabs",
  type: "object",
  fields: [
    // Tab Labels
    { name: "overview", title: "OVERVIEW", type: "string" },
    { name: "techSpecification", title: "TECH SPECIFICATION", type: "string" },
    { name: "vehicles", title: "VEHICLES", type: "string" },
    { name: "gallery", title: "GALLERY", type: "string" },
    { name: "offers", title: "OFFERS", type: "string" },
    { name: "configure", title: "CONFIGURE", type: "string" },

    // Tab Descriptions
    { name: "vehicleDetails", title: "Vehicle details", type: "string" },
    { name: "technicalDetails", title: "Technical details", type: "string" },
    { name: "availableModels", title: "Available models", type: "string" },
    { name: "photosAndVideos", title: "Photos & videos", type: "string" },
    { name: "specialOffers", title: "Special offers & promotions", type: "string" },
    { name: "buildAndPrice", title: "Build & price", type: "string" }
  ]
})
```

**Group 4: Vehicle Model Content** (`vehicleModelContent`)
```typescript
// Create object: src/sanity/schemas/objects/vehicle-model-content.ts
export default defineType({
  name: "vehicleModelContent",
  title: "Vehicle Model Content",
  type: "object",
  fields: [
    // Section Headers
    { name: "overview", title: "OVERVIEW", type: "string" },
    { name: "details", title: "DETAILS", type: "string" },

    // Contact Section
    { name: "interestedIn", title: "Interested in this", type: "string" },
    { name: "specialistsCanHelp", title: "specialists can help you configure, finance, and schedule a test drive.", type: "string" },
    { name: "testDrive", title: "Test Drive", type: "string" },
    { name: "contact", title: "Contact", type: "string" },

    // Alt Text Patterns
    { name: "logoAltText", title: "logo", type: "string" },
    { name: "imageAltText", title: "image", type: "string" }
  ]
})
```

**Hardcoded strings to move**:
- `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx`:
  - "Book Test Drive", "Find Dealer", "Contact Sales", "Get Brochure"
  - "Experience the vehicle", "Locate nearest dealer", "Speak with expert", "Download materials"
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-navigation.tsx`:
  - "Overview", "Gallery", "Configure", "Compare"
  - "Vehicle details", "Photos & videos", "Build & price", "VS competitors"
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-tabs.tsx`:
  - "OVERVIEW", "TECH SPECIFICATION", "VEHICLES", "GALLERY", "OFFERS", "CONFIGURE"
  - "Vehicle details", "Technical details", "Available models", "Photos & videos", "Special offers & promotions", "Build & price"
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx`:
  - "Book Test Drive", "Configure", "Compare", "Find Dealer", "Contact Sales", "Share Vehicle"
  - "Experience this vehicle", "Build & price", "VS competitors", "Locate nearest dealer", "Speak with expert", "Send to friend"
  - "Interested in this", "Our", "specialists can help you configure, finance, and schedule a test drive."
  - "Test Drive", "Contact"
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-overview.tsx`:
  - "OVERVIEW", "DETAILS"
  - Alt text patterns: `${name} logo`, `${name} image`
- `src/app/(frontend)/gammes/_components/brand-grid.tsx`:
  - "No brands found", "Check back later for new brands."
- `src/app/(frontend)/gammes/_components/brand-card.tsx`:
  - Alt text generation: `${name} logo`
  - Fallback character display: `${name?.charAt(0) || "?"}`
- `src/components/global/navigation/navigation-items.tsx`:
  - "No child pages yet"

---

## 2. VEHICLE SETTINGS SINGLETON (NEW)

### 2.1 Create Vehicle Settings Singleton
**File to create**: `src/sanity/schemas/singletons/vehicle-settings.ts`
**Following**: Dictionary system with grouped objects for better organization

This singleton will contain all vehicle-related global settings and labels:

#### Group 1: Vehicle Specifications (`vehicleSpecs`)
```typescript
// Create object: src/sanity/schemas/objects/vehicle-specifications.ts
export default defineType({
  name: "vehicleSpecifications",
  title: "Vehicle Specifications",
  type: "object",
  fields: [
    { name: "fuelType", title: "Fuel Type", type: "string" },
    { name: "transmission", title: "Transmission", type: "string" },
    { name: "power", title: "Power", type: "string" },
    { name: "mileage", title: "Mileage", type: "string" },
    { name: "year", title: "Year", type: "string" },
    { name: "condition", title: "Condition", type: "string" }
  ]
})
```

#### Group 2: Condition Labels (`conditionLabels`)
```typescript
// Create object: src/sanity/schemas/objects/condition-labels.ts
export default defineType({
  name: "conditionLabels",
  title: "Condition Labels",
  type: "object",
  fields: [
    { name: "new", title: "New", type: "string" },
    { name: "used", title: "Used", type: "string" },
    { name: "demonstration", title: "Demonstration", type: "string" },
    { name: "oldtimer", title: "Oldtimer", type: "string" },
    { name: "preRegistered", title: "Pre-registered", type: "string" }
  ]
})
```

#### Group 3: Fuel Type Labels (`fuelTypeLabels`)
```typescript
// Create object: src/sanity/schemas/objects/fuel-type-labels.ts
export default defineType({
  name: "fuelTypeLabels",
  title: "Fuel Type Labels",
  type: "object",
  fields: [
    { name: "petrol", title: "Petrol", type: "string" },
    { name: "diesel", title: "Diesel", type: "string" },
    { name: "electric", title: "Electric", type: "string" },
    { name: "hevPetrol", title: "Hybrid Petrol", type: "string" },
    { name: "hevDiesel", title: "Hybrid Diesel", type: "string" },
    { name: "phevPetrol", title: "Plug-in Hybrid Petrol", type: "string" },
    { name: "phevDiesel", title: "Plug-in Hybrid Diesel", type: "string" },
    { name: "cngPetrol", title: "CNG/Petrol", type: "string" },
    { name: "lpgPetrol", title: "LPG/Petrol", type: "string" },
    { name: "hydrogen", title: "Hydrogen", type: "string" },
    { name: "ethanolPetrol", title: "Ethanol", type: "string" },
    { name: "mhevDiesel", title: "Micro-hybrid Diesel", type: "string" },
    { name: "mhevPetrol", title: "Micro-hybrid Petrol", type: "string" },
    { name: "twoStrokeMixture", title: "Two-stroke Engine", type: "string" }
  ]
})
```

#### Group 4: Fuel Type Groups (`fuelTypeGroups`)
```typescript
// Create object: src/sanity/schemas/objects/fuel-type-groups.ts
export default defineType({
  name: "fuelTypeGroups",
  title: "Fuel Type Groups",
  type: "object",
  fields: [
    { name: "petrol", title: "Petrol", type: "string" },
    { name: "diesel", title: "Diesel", type: "string" },
    { name: "electric", title: "Electric", type: "string" },
    { name: "hybrid", title: "Hybrid", type: "string" },
    { name: "other", title: "Other", type: "string" }
  ]
})
```

#### Group 5: Vehicle Filter Labels (`vehicleFilterLabels`)
```typescript
// Create object: src/sanity/schemas/objects/vehicle-filter-labels.ts
export default defineType({
  name: "vehicleFilterLabels",
  title: "Vehicle Filter Labels",
  type: "object",
  fields: [
    { name: "filters", title: "Filters", type: "string" },
    { name: "clearAll", title: "Clear All", type: "string" },
    { name: "removeFilter", title: "Remove Filter", type: "string" },
    { name: "showResults", title: "Show Results", type: "string" },
    { name: "viewResults", title: "View Results", type: "string" },
    { name: "loading", title: "Loading", type: "string" },
    { name: "vehiclesAvailable", title: "vehicles available", type: "string" },
    { name: "noVehiclesFound", title: "No vehicles found", type: "string" }
  ]
})
```

#### Group 6: Vehicle Search Labels (`vehicleSearchLabels`)
```typescript
// Create object: src/sanity/schemas/objects/vehicle-search-labels.ts
export default defineType({
  name: "vehicleSearchLabels",
  title: "Vehicle Search Labels",
  type: "object",
  fields: [
    { name: "searchVehicles", title: "Search vehicles", type: "string" },
    { name: "searchPlaceholder", title: "Search placeholder", type: "string" },
    { name: "clearSearch", title: "Clear search", type: "string" },
    { name: "noResultsFound", title: "No results found", type: "string" }
  ]
})
```

**Hardcoded strings to move**:
- `src/components/vehicles/card/constants.ts`:
  - "Carburant", "Transmission", "Puissance", "Kilométrage"
  - "Neuf", "Occasion", "Démonstration", "Oldtimer", "Pré-immatriculé"
- `src/components/vehicles/filters/components/ConditionContent.tsx`:
  - All condition labels
- `src/components/vehicles/filters/components/ConditionLabel.tsx`:
  - All condition labels
- `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx`:
  - "Essence", "Diesel", "Électrique", "Hybride essence", "Hybride diesel", "Hybride rechargeable essence", "Hybride rechargeable diesel", "GNC/Essence", "GPL/Essence", "Hydrogène", "Éthanol", "Micro-hybride diesel", "Micro-hybride essence", "Moteur à deux temps"
  - Group labels: "Essence", "Diesel", "Électrique", "Hybride", "Autre"
- `src/components/vehicles/filters/components/FilterHeader.tsx`:
  - "Filters"
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`:
  - "Voir les résultats", "Chargement...", "véhicules disponibles"

---

## 2. VEHICLE SETTINGS SINGLETON (NEW)

### 2.1 Create Vehicle Settings Singleton
**File to create**: `src/sanity/schemas/singletons/vehicle-settings.ts`

This singleton will contain all vehicle-related global settings and labels:

```typescript
export default defineType({
  name: "vehicleSettings",
  title: "Vehicle Settings",
  type: "document",
  fields: [
    // Vehicle Specifications Labels
    defineField({
      name: "specifications",
      title: "Specifications",
      type: "object",
      fields: [
        { name: "fuelType", title: "Fuel Type", type: "string" },
        { name: "transmission", title: "Transmission", type: "string" },
        { name: "power", title: "Power", type: "string" },
        { name: "mileage", title: "Mileage", type: "string" },
        { name: "year", title: "Year", type: "string" },
        { name: "condition", title: "Condition", type: "string" }
      ]
    }),

    // Condition Labels
    defineField({
      name: "conditionLabels",
      title: "Condition Labels",
      type: "object",
      fields: [
        { name: "new", title: "New", type: "string" },
        { name: "used", title: "Used", type: "string" },
        { name: "demonstration", title: "Demonstration", type: "string" },
        { name: "oldtimer", title: "Oldtimer", type: "string" },
        { name: "preRegistered", title: "Pre-registered", type: "string" }
      ]
    }),

    // Fuel Type Labels (comprehensive list)
    defineField({
      name: "fuelTypeLabels",
      title: "Fuel Type Labels",
      type: "object",
      fields: [
        { name: "petrol", title: "Petrol", type: "string" },
        { name: "diesel", title: "Diesel", type: "string" },
        { name: "electric", title: "Electric", type: "string" },
        { name: "hevPetrol", title: "Hybrid Petrol", type: "string" },
        { name: "hevDiesel", title: "Hybrid Diesel", type: "string" },
        { name: "phevPetrol", title: "Plug-in Hybrid Petrol", type: "string" },
        { name: "phevDiesel", title: "Plug-in Hybrid Diesel", type: "string" },
        { name: "cngPetrol", title: "CNG/Petrol", type: "string" },
        { name: "lpgPetrol", title: "LPG/Petrol", type: "string" },
        { name: "hydrogen", title: "Hydrogen", type: "string" },
        { name: "ethanolPetrol", title: "Ethanol", type: "string" },
        { name: "mhevDiesel", title: "Micro-hybrid Diesel", type: "string" },
        { name: "mhevPetrol", title: "Micro-hybrid Petrol", type: "string" },
        { name: "twoStrokeMixture", title: "Two-stroke Engine", type: "string" }
      ]
    }),

    // Fuel Type Group Labels
    defineField({
      name: "fuelTypeGroups",
      title: "Fuel Type Groups",
      type: "object",
      fields: [
        { name: "petrol", title: "Petrol", type: "string" },
        { name: "diesel", title: "Diesel", type: "string" },
        { name: "electric", title: "Electric", type: "string" },
        { name: "hybrid", title: "Hybrid", type: "string" },
        { name: "other", title: "Other", type: "string" }
      ]
    }),

    // Filter UI Labels
    defineField({
      name: "filterLabels",
      title: "Filter Labels",
      type: "object",
      fields: [
        { name: "filters", title: "Filters", type: "string" },
        { name: "clearAll", title: "Clear All", type: "string" },
        { name: "removeFilter", title: "Remove Filter", type: "string" },
        { name: "showResults", title: "Show Results", type: "string" },
        { name: "viewResults", title: "View Results", type: "string" },
        { name: "loading", title: "Loading", type: "string" },
        { name: "vehiclesAvailable", title: "vehicles available", type: "string" },
        { name: "noVehiclesFound", title: "No vehicles found", type: "string" }
      ]
    }),

    // Search Labels
    defineField({
      name: "searchLabels",
      title: "Search Labels",
      type: "object",
      fields: [
        { name: "searchVehicles", title: "Search vehicles", type: "string" },
        { name: "searchPlaceholder", title: "Search placeholder", type: "string" },
        { name: "clearSearch", title: "Clear search", type: "string" },
        { name: "noResultsFound", title: "No results found", type: "string" }
      ]
    })
  ]
})
```

**Hardcoded strings to move**:
- `src/components/vehicles/card/constants.ts`: "Carburant", "Transmission", "Puissance", "Kilométrage"
- `src/components/vehicles/filters/components/ConditionContent.tsx`: All condition labels
- `src/components/vehicles/filters/components/ConditionLabel.tsx`: All condition labels
- `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx`: All fuel type labels and groups
- `src/components/vehicles/filters/components/FilterHeader.tsx`: "Filters"
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`: "Voir les résultats", "Chargement...", "véhicules disponibles"

---

## 3. GLOBAL STRINGS SINGLETON (NEW)

### 3.1 Create Global Translations Singleton
**File to create**: `src/sanity/schemas/singletons/global-translations.ts`

This singleton will contain all global UI strings that don't belong to specific documents:

```typescript
export default defineType({
  name: "globalTranslations",
  title: "Global Translations",
  type: "document",
  fields: [
    // Navigation & Layout
    defineField({
      name: "navigation",
      title: "Navigation",
      type: "object",
      fields: [
        { name: "menu", title: "Menu", type: "string" },
        { name: "home", title: "Home", type: "string" },
        { name: "back", title: "Back", type: "string" },
        { name: "close", title: "Close", type: "string" }
      ]
    }),

    // Common Actions
    defineField({
      name: "commonActions",
      title: "Common Actions",
      type: "object",
      fields: [
        { name: "search", title: "Search", type: "string" },
        { name: "filter", title: "Filter", type: "string" },
        { name: "sort", title: "Sort", type: "string" },
        { name: "clear", title: "Clear", type: "string" },
        { name: "apply", title: "Apply", type: "string" },
        { name: "cancel", title: "Cancel", type: "string" },
        { name: "save", title: "Save", type: "string" },
        { name: "edit", title: "Edit", type: "string" },
        { name: "delete", title: "Delete", type: "string" },
        { name: "view", title: "View", type: "string" },
        { name: "readMore", title: "Read More", type: "string" },
        { name: "learnMore", title: "Learn More", type: "string" }
      ]
    }),

    // Status Messages & Error Handling
    defineField({
      name: "statusMessages",
      title: "Status Messages",
      type: "object",
      fields: [
        { name: "loading", title: "Loading", type: "string" },
        { name: "error", title: "Error", type: "string" },
        { name: "success", title: "Success", type: "string" },
        { name: "noResults", title: "No Results", type: "string" },
        { name: "tryAgain", title: "Try Again", type: "string" },
        { name: "retry", title: "Retry", type: "string" },
        { name: "errorLoadingVehicles", title: "Error Loading Vehicles", type: "string" },
        { name: "unexpectedError", title: "An unexpected error occurred", type: "string" },
        { name: "noHomepageSet", title: "No Homepage Set...", type: "string" }
      ]
    }),

    // Form Validation
    defineField({
      name: "formValidation",
      title: "Form Validation",
      type: "object",
      fields: [
        { name: "required", title: "is required", type: "string" },
        { name: "invalidEmail", title: "Invalid email address", type: "string" },
        { name: "submitError", title: "Submit Error", type: "string" },
        { name: "submitSuccess", title: "Submit Success", type: "string" }
      ]
    }),

    // Development/Demo strings
    defineField({
      name: "development",
      title: "Development",
      type: "object",
      fields: [
        { name: "siteEngine", title: "SiteEngine", type: "string" },
        { name: "runCommand", title: "Run the command to install demo content", type: "string" },
        { name: "disableDraftMode", title: "Disable Draft Mode", type: "string" },
        { name: "defaultMetaDescription", title: "Default Meta Description", type: "string" }
      ]
    })
  ]
})
```

**Hardcoded strings to move**:
- `src/components/shared/install-demo-button.tsx`: "SiteEngine", "Run the command to install demo content"
- `src/components/shared/disable-draft-mode.tsx`: "Disable Draft Mode"
- `src/app/(frontend)/layout.tsx`: "Open-Source Next.js & Sanity Marketing Website Template."
- `src/app/(frontend)/page.tsx`: "No Homepage Set..."
- `src/components/vehicles/blocks/states/vehicle-display-error.tsx`: Error messages
- `src/components/shared/form.tsx`: Form validation messages

---

## 4. COMPREHENSIVE HARDCODED STRINGS INVENTORY

### 4.1 Gammes (Brands) Directory - EXTENSIVE FINDINGS
**Files with hardcoded strings**:

#### `src/app/(frontend)/gammes/_components/brand-grid.tsx`
- "No brands found"
- "Check back later for new brands."

#### `src/app/(frontend)/gammes/_components/brand-card.tsx`
- Alt text generation: `${name} logo`
- Fallback character display: `${name?.charAt(0) || "?"}`

#### `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx`
- "Book Test Drive"
- "Find Dealer"
- "Contact Sales"
- "Get Brochure"
- "Experience the vehicle"
- "Locate nearest dealer"
- "Speak with expert"
- "Download materials"

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx`
- "Book Test Drive"
- "Configure"
- "Compare"
- "Find Dealer"
- "Contact Sales"
- "Share Vehicle"
- "Experience this vehicle"
- "Build & price"
- "VS competitors"
- "Locate nearest dealer"
- "Speak with expert"
- "Send to friend"

### 4.2 Vehicle Components - EXTENSIVE FINDINGS
**Files with hardcoded strings**:

#### `src/components/vehicles/card/constants.ts`
- "Carburant" (Fuel)
- "Transmission"
- "Puissance" (Power)
- "Kilométrage" (Mileage)
- "Neuf" (New)
- "Occasion" (Used)
- "Démonstration" (Demonstration)
- "Oldtimer"
- "Pré-immatriculé" (Pre-registered)

#### `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx`
- "Essence" (Petrol)
- "Diesel"
- "Électrique" (Electric)
- "Hybride essence" (Hybrid petrol)
- "Hybride diesel" (Hybrid diesel)
- "Hybride rechargeable essence" (Plug-in hybrid petrol)
- "Hybride rechargeable diesel" (Plug-in hybrid diesel)
- "GNC/Essence" (CNG/Petrol)
- "GPL/Essence" (LPG/Petrol)
- "Hydrogène" (Hydrogen)
- "Éthanol" (Ethanol)
- "Micro-hybride diesel" (Micro-hybrid diesel)
- "Micro-hybride essence" (Micro-hybrid petrol)
- "Moteur à deux temps" (Two-stroke engine)
- Group labels: "Essence", "Diesel", "Électrique", "Hybride", "Autre"

#### `src/components/vehicles/filters/components/FilterHeader.tsx`
- "Filters"

#### `src/components/vehicles/filters/components/FilterChips.tsx`
- "Remove ${filter.displayKey} filter" (aria-label)

#### `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`
- "Voir les résultats" (View results)
- "Chargement..." (Loading...)
- "${count} véhicules disponibles" (vehicles available)

#### `src/components/vehicles/blocks/states/vehicle-display-error.tsx`
- "Error Loading Vehicles"
- "An unexpected error occurred while loading vehicles. Please try again."
- "Retry"

### 4.3 Blog Components - MISSING FROM ORIGINAL
**Files with hardcoded strings**:

#### `src/app/(frontend)/blog/_components/blog-search.tsx`
- Search input placeholder (implicit)
- Clear search functionality (implicit)

### 4.4 Search Components - MISSING FROM ORIGINAL
**Files with hardcoded strings**:

#### `src/app/(frontend)/search/client.tsx`
- Sheet component labels (implicit from UI components)

### 4.5 Navigation Components - MISSING FROM ORIGINAL
**Files with hardcoded strings**:

#### `src/components/global/navigation/navigation-items.tsx`
- "No child pages yet"

### 4.6 Shared Components - MISSING FROM ORIGINAL
**Files with hardcoded strings**:

#### `src/components/shared/install-demo-button.tsx`
- "SiteEngine"
- "Run the command to install demo content"
- Command text: "npx sanity dataset import demo-content.tar.gz production"

#### `src/components/shared/disable-draft-mode.tsx`
- "Disable Draft Mode"

#### `src/components/shared/form.tsx`
- "is required" (validation)
- "Invalid email address" (validation)

### 4.7 Layout & Page Components - MISSING FROM ORIGINAL
**Files with hardcoded strings**:

#### `src/app/(frontend)/layout.tsx`
- "Open-Source Next.js & Sanity Marketing Website Template." (metadata description)

#### `src/app/(frontend)/page.tsx`
- "No Homepage Set..."

#### `src/app/(frontend)/vehicles/[id]/_components/vehicle-title.tsx`
- Locale formatting: `vehicle.mileage.toLocaleString("fr-CH")`
- Unit display: " km", " • " (separators)

---

## 5. IMPLEMENTATION PLAN (REVISED)

### Phase 1: Update Existing Singletons (2-3 days)
1. **Update brands-page.ts** - Add brandSettings group with all brand/vehicle model actions
2. **Update offers-page.ts** - Add offerSettings group with offer-specific strings
3. **Update blog-page.ts** - Add blogSettings group with blog-specific strings
4. **Update search-page.ts** - Add searchSettings group with search-specific strings

### Phase 2: Create Vehicle Settings Singleton (1-2 days)
1. **Create vehicle-settings.ts** - Centralized vehicle specifications, conditions, fuel types, filter labels
2. **Add to schema index** - Register new singleton
3. **Create queries** - Add GraphQL queries for vehicle settings

### Phase 3: Create Global Translations Singleton (1-2 days)
1. **Create global-translations.ts** - Navigation, common actions, status messages, form validation, development strings
2. **Add to schema index** - Register new singleton
3. **Create queries** - Add GraphQL queries for global strings

### Phase 4: Update Frontend Components (4-5 days)
1. **Create translation hooks** - `useGlobalTranslations()`, `useVehicleSettings()`, `useBrandSettings()`, etc.
2. **Update 20+ components** - Replace all hardcoded strings with CMS data
3. **Add fallbacks** - Ensure graceful degradation if CMS data missing
4. **Update locale formatting** - Make locale configurable

### Phase 5: Testing & Validation (2-3 days)
1. **Test all languages** - Verify EN/FR switching works across all components
2. **Test missing data** - Ensure fallbacks work properly
3. **Performance check** - Verify no performance regression
4. **Accessibility check** - Ensure aria-labels are properly translated

---

## 6. TECHNICAL NOTES

### 6.1 Query Strategy
- Use server-side data fetching where possible (following user preference)
- Pass translation data as props to client components
- Cache translation data to minimize API calls
- Consider using React Context for deeply nested components

### 6.2 Fallback Strategy
```typescript
// Example fallback pattern with proper typing
const label = vehicleSettings?.conditionLabels?.new ?? "New";
const actionLabel = brandSettings?.bookTestDrive ?? "Book Test Drive";
```

### 6.3 Type Safety
- Generate TypeScript types from Sanity schemas
- Use strict typing for translation keys
- Implement compile-time checks for missing translations
- Create utility types for translation objects

---

## 7. FILES TO UPDATE (COMPREHENSIVE LIST)

### Frontend Components (25+ files to update):
- `src/app/(frontend)/layout.tsx` - Metadata description
- `src/app/(frontend)/page.tsx` - "No Homepage Set" message
- `src/app/(frontend)/gammes/_components/brand-grid.tsx` - Empty state messages
- `src/app/(frontend)/gammes/_components/brand-card.tsx` - Alt text generation
- `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx` - All action labels
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx` - All action labels
- `src/app/(frontend)/vehicles/[id]/_components/vehicle-title.tsx` - Locale formatting
- `src/components/vehicles/card/constants.ts` - All specification labels
- `src/components/vehicles/filters/components/ConditionContent.tsx` - All condition labels
- `src/components/vehicles/filters/components/ConditionLabel.tsx` - All condition labels
- `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx` - All fuel type labels
- `src/components/vehicles/filters/components/FilterHeader.tsx` - "Filters" title
- `src/components/vehicles/filters/components/FilterChips.tsx` - Remove filter labels
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx` - Count messages
- `src/components/vehicles/blocks/states/vehicle-display-error.tsx` - Error messages
- `src/components/global/navigation/navigation-items.tsx` - "No child pages yet"
- `src/components/shared/form.tsx` - Validation messages
- `src/components/shared/install-demo-button.tsx` - Demo installation text
- `src/components/shared/disable-draft-mode.tsx` - "Disable Draft Mode"

### Sanity Schema Files (4 new singletons + updates):
- `src/sanity/schemas/singletons/pages/brands-page.ts` - Add brandSettings group
- `src/sanity/schemas/singletons/pages/offers-page.ts` - Add offerSettings group
- `src/sanity/schemas/singletons/pages/blog-page.ts` - Add blogSettings group
- `src/sanity/schemas/singletons/pages/search-page.ts` - Add searchSettings group
- `src/sanity/schemas/singletons/vehicle-settings.ts` - **NEW FILE**
- `src/sanity/schemas/singletons/global-translations.ts` - **NEW FILE**
- `src/sanity/schemas/index.ts` - Register new schemas

### Query Files (Add new queries):
- `src/sanity/lib/queries/singletons/vehicle-settings.ts` - **NEW FILE**
- `src/sanity/lib/queries/singletons/global-translations.ts` - **NEW FILE**
- Update existing page queries to include new translation fields

---

## 8. ESTIMATED EFFORT (REVISED)

- **Phase 1**: 2-3 days (Update existing singletons)
- **Phase 2**: 1-2 days (Vehicle settings singleton)
- **Phase 3**: 1-2 days (Global translations singleton)
- **Phase 4**: 4-5 days (Frontend component updates - 25+ files)
- **Phase 5**: 2-3 days (Testing and validation)

**Total**: 10-15 days for complete internationalization implementation

---

*This comprehensive checklist captures ALL hardcoded strings found in the frontend application and provides a structured approach to move them to appropriate Sanity singletons, following the existing architecture patterns while enabling full multilingual support.*