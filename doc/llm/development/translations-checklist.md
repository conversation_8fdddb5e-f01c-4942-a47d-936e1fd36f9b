# AutoCorner Website - Translations Checklist

## Overview
This document catalogs all hardcoded text strings found in the frontend that need to be moved to Sanity CMS for proper internationalization support.

## Current Translation Status
✅ **Sanity Studio**: Fully translated (EN/FR) with comprehensive dictionary system
❌ **Frontend Components**: Contains hardcoded strings that need to be moved to Sanity
❌ **Global Strings**: Need centralized management in new Sanity singleton

---

## 1. HARDCODED STRINGS IN EXISTING SANITY DOCUMENTS

### 1.1 Vehicle-Related Content (Can be added to existing documents)

#### **Vehicle Models Document** (`src/sanity/schemas/documents/vehicle-model.ts`)
**Current Status**: Schema exists but missing translatable content fields
**Recommended Updates**:
```typescript
// Add these fields to vehicle-model.ts
defineField({
  name: "specifications",
  title: "Specifications",
  type: "object",
  fields: [
    { name: "fuelTypeLabel", title: "Fuel Type Label", type: "string" },
    { name: "transmissionLabel", title: "Transmission Label", type: "string" },
    { name: "conditionLabels", title: "Condition Labels", type: "object", fields: [
      { name: "new", title: "New", type: "string" },
      { name: "used", title: "Used", type: "string" },
      { name: "demonstration", title: "Demonstration", type: "string" },
      { name: "oldtimer", title: "Oldtimer", type: "string" },
      { name: "preRegistered", title: "Pre-registered", type: "string" }
    ]}
  ]
})
```

**Hardcoded strings to move**:
- `src/components/vehicles/filters/components/ConditionContent.tsx`:
  - "Neuf", "Occasion", "Véhicule de démonstration", "Oldtimer", "Pré-immatriculé"
- `src/components/vehicles/card/constants.ts`:
  - "Carburant", "Transmission" labels

#### **Center Document** (`src/sanity/schemas/documents/center.ts`)
**Current Status**: Schema exists but missing UI labels
**Recommended Updates**:
```typescript
// Add these fields to center.ts
defineField({
  name: "uiLabels",
  title: "UI Labels",
  type: "object",
  fields: [
    { name: "contactSales", title: "Contact Sales", type: "string" },
    { name: "findDealer", title: "Find Dealer", type: "string" },
    { name: "bookTestDrive", title: "Book Test Drive", type: "string" }
  ]
})
```

### 1.2 Form Document Updates
**Current Status**: Form schema exists with basic fields
**File**: `src/sanity/schemas/documents/form.ts`
**Recommended Updates**:
```typescript
// Add validation message fields
defineField({
  name: "validationMessages",
  title: "Validation Messages",
  type: "object",
  fields: [
    { name: "required", title: "Required Field Message", type: "string" },
    { name: "invalidEmail", title: "Invalid Email Message", type: "string" },
    { name: "submitError", title: "Submit Error Message", type: "string" },
    { name: "submitSuccess", title: "Submit Success Message", type: "string" }
  ]
})
```

**Hardcoded strings to move**:
- `src/components/shared/form.tsx`:
  - "is required", "Invalid email address" validation messages

---

## 2. NEW SANITY OBJECTS NEEDED

### 2.1 Vehicle Action Labels Object
**File to create**: `src/sanity/schemas/objects/vehicle-action-labels.ts`
```typescript
export default defineType({
  name: "vehicleActionLabels",
  title: "Vehicle Action Labels",
  type: "object",
  fields: [
    { name: "bookTestDrive", title: "Book Test Drive", type: "string" },
    { name: "configure", title: "Configure", type: "string" },
    { name: "compare", title: "Compare", type: "string" },
    { name: "findDealer", title: "Find Dealer", type: "string" },
    { name: "contactSales", title: "Contact Sales", type: "string" },
    { name: "shareVehicle", title: "Share Vehicle", type: "string" },
    { name: "experienceVehicle", title: "Experience this vehicle", type: "string" },
    { name: "buildAndPrice", title: "Build & price", type: "string" },
    { name: "vsCompetitors", title: "VS competitors", type: "string" },
    { name: "locateNearestDealer", title: "Locate nearest dealer", type: "string" },
    { name: "speakWithExpert", title: "Speak with expert", type: "string" },
    { name: "sendToFriend", title: "Send to friend", type: "string" }
  ]
})
```

**Hardcoded strings to move**:
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx`:
  - "Book Test Drive", "Configure", "Compare", "Find Dealer", "Contact Sales", "Share Vehicle"
  - "Experience this vehicle", "Build & price", "VS competitors", etc.

### 2.2 Vehicle Filter Labels Object
**File to create**: `src/sanity/schemas/objects/vehicle-filter-labels.ts`
```typescript
export default defineType({
  name: "vehicleFilterLabels",
  title: "Vehicle Filter Labels",
  type: "object",
  fields: [
    { name: "filtersTitle", title: "Filters Title", type: "string" },
    { name: "clearAll", title: "Clear All", type: "string" },
    { name: "removeFilter", title: "Remove Filter", type: "string" },
    { name: "showResults", title: "Show Results", type: "string" },
    { name: "loading", title: "Loading", type: "string" },
    { name: "vehiclesAvailable", title: "vehicles available", type: "string" }
  ]
})
```

**Hardcoded strings to move**:
- `src/components/vehicles/filters/components/FilterHeader.tsx`: "Filters"
- `src/components/vehicles/filters/components/FilterChips.tsx`: "Remove {filter} filter"
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`:
  - "Voir les résultats", "Chargement...", "véhicules disponibles"

### 2.3 Error Messages Object
**File to create**: `src/sanity/schemas/objects/error-messages.ts`
```typescript
export default defineType({
  name: "errorMessages",
  title: "Error Messages",
  type: "object",
  fields: [
    { name: "errorLoadingVehicles", title: "Error Loading Vehicles", type: "string" },
    { name: "unexpectedError", title: "Unexpected Error Message", type: "string" },
    { name: "retry", title: "Retry", type: "string" },
    { name: "noHomepageSet", title: "No Homepage Set", type: "string" }
  ]
})
```

**Hardcoded strings to move**:
- `src/components/vehicles/blocks/states/vehicle-display-error.tsx`:
  - "Error Loading Vehicles", "An unexpected error occurred...", "Retry"
- `src/app/(frontend)/page.tsx`: "No Homepage Set..."

---

## 3. GLOBAL STRINGS SINGLETON

### 3.1 Create Global Translations Singleton
**File to create**: `src/sanity/schemas/singletons/global-translations.ts`

This singleton will contain all global UI strings that don't belong to specific documents:

```typescript
export default defineType({
  name: "globalTranslations",
  title: "Global Translations",
  type: "document",
  fields: [
    // Navigation & Layout
    defineField({
      name: "navigation",
      title: "Navigation",
      type: "object",
      fields: [
        { name: "menu", title: "Menu", type: "string" },
        { name: "home", title: "Home", type: "string" },
        { name: "back", title: "Back", type: "string" }
      ]
    }),

    // Common Actions
    defineField({
      name: "commonActions",
      title: "Common Actions",
      type: "object",
      fields: [
        { name: "search", title: "Search", type: "string" },
        { name: "filter", title: "Filter", type: "string" },
        { name: "sort", title: "Sort", type: "string" },
        { name: "clear", title: "Clear", type: "string" },
        { name: "apply", title: "Apply", type: "string" },
        { name: "cancel", title: "Cancel", type: "string" },
        { name: "save", title: "Save", type: "string" },
        { name: "edit", title: "Edit", type: "string" },
        { name: "delete", title: "Delete", type: "string" },
        { name: "view", title: "View", type: "string" },
        { name: "close", title: "Close", type: "string" }
      ]
    }),

    // Status Messages
    defineField({
      name: "statusMessages",
      title: "Status Messages",
      type: "object",
      fields: [
        { name: "loading", title: "Loading", type: "string" },
        { name: "error", title: "Error", type: "string" },
        { name: "success", title: "Success", type: "string" },
        { name: "noResults", title: "No Results", type: "string" },
        { name: "tryAgain", title: "Try Again", type: "string" }
      ]
    }),

    // Vehicle-specific globals
    defineField({
      name: "vehicleGlobals",
      title: "Vehicle Globals",
      type: "object",
      fields: [
        { name: "vehiclesAvailable", title: "vehicles available", type: "string" },
        { name: "showResults", title: "Show Results", type: "string" },
        { name: "viewAllResults", title: "View All Results", type: "string" }
      ]
    }),

    // Development/Demo strings
    defineField({
      name: "development",
      title: "Development",
      type: "object",
      fields: [
        { name: "installDemoContent", title: "Install Demo Content Message", type: "string" },
        { name: "runCommand", title: "Run the command to install demo content", type: "string" },
        { name: "disableDraftMode", title: "Disable Draft Mode", type: "string" }
      ]
    })
  ]
})
```

### 3.2 Global Strings to Move

**From various components**:
- `src/components/shared/install-demo-button.tsx`:
  - "Run the command to install demo content"
- `src/components/shared/disable-draft-mode.tsx`:
  - "Disable Draft Mode"
- `src/app/(frontend)/layout.tsx`:
  - Default metadata description: "Open-Source Next.js & Sanity Marketing Website Template."

---

## 4. IMPLEMENTATION PLAN

### Phase 1: Update Existing Documents
1. **Update vehicle-model.ts** - Add specification labels
2. **Update center.ts** - Add UI action labels
3. **Update form.ts** - Add validation messages
4. **Update general-settings.ts** - Add global metadata strings

### Phase 2: Create New Objects
1. **Create vehicle-action-labels.ts** - Vehicle interaction buttons
2. **Create vehicle-filter-labels.ts** - Filter UI strings
3. **Create error-messages.ts** - Error handling strings

### Phase 3: Create Global Singleton
1. **Create global-translations.ts** - Centralized global strings
2. **Add to schema index** - Register new singleton
3. **Create queries** - Add GraphQL queries for global strings

### Phase 4: Update Frontend Components
1. **Create translation hooks** - `useGlobalTranslations()`, `useVehicleLabels()`
2. **Update components** - Replace hardcoded strings with CMS data
3. **Add fallbacks** - Ensure graceful degradation if CMS data missing

### Phase 5: Testing & Validation
1. **Test all languages** - Verify EN/FR switching works
2. **Test missing data** - Ensure fallbacks work properly
3. **Performance check** - Verify no performance regression

---

## 5. TECHNICAL NOTES

### 5.1 Query Strategy
- Use server-side data fetching where possible (following user preference)
- Pass translation data as props to client components
- Cache translation data to minimize API calls

### 5.2 Fallback Strategy
```typescript
// Example fallback pattern
const label = vehicleLabels?.bookTestDrive ?? "Book Test Drive";
```

### 5.3 Type Safety
- Generate TypeScript types from Sanity schemas
- Use strict typing for translation keys
- Implement compile-time checks for missing translations

---

## 6. FILES TO UPDATE

### Frontend Components (Replace hardcoded strings):
- `src/app/(frontend)/layout.tsx` - Metadata description
- `src/app/(frontend)/page.tsx` - "No Homepage Set" message
- `src/components/vehicles/filters/components/ConditionContent.tsx` - Condition labels
- `src/components/vehicles/filters/components/FilterHeader.tsx` - "Filters" title
- `src/components/vehicles/filters/components/FilterChips.tsx` - Remove filter labels
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx` - Count messages
- `src/components/vehicles/blocks/states/vehicle-display-error.tsx` - Error messages
- `src/components/vehicles/card/constants.ts` - Spec labels
- `src/components/shared/form.tsx` - Validation messages
- `src/components/shared/install-demo-button.tsx` - Demo installation text
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx` - Action labels

### Sanity Schema Files (Add new fields/objects):
- `src/sanity/schemas/documents/vehicle-model.ts` - Add specification labels
- `src/sanity/schemas/documents/center.ts` - Add UI labels
- `src/sanity/schemas/documents/form.ts` - Add validation messages
- `src/sanity/schemas/singletons/general-settings.ts` - Add global metadata
- `src/sanity/schemas/objects/vehicle-action-labels.ts` - **NEW FILE**
- `src/sanity/schemas/objects/vehicle-filter-labels.ts` - **NEW FILE**
- `src/sanity/schemas/objects/error-messages.ts` - **NEW FILE**
- `src/sanity/schemas/singletons/global-translations.ts` - **NEW FILE**
- `src/sanity/schemas/index.ts` - Register new schemas

### Query Files (Add new queries):
- `src/sanity/lib/queries/singletons/global-translations.ts` - **NEW FILE**
- Update existing queries to include new translation fields

---

## 7. ESTIMATED EFFORT

- **Phase 1-2**: 2-3 days (Schema updates and new objects)
- **Phase 3**: 1-2 days (Global singleton creation)
- **Phase 4**: 3-4 days (Frontend component updates)
- **Phase 5**: 1-2 days (Testing and validation)

**Total**: 7-11 days for complete internationalization implementation

---

*This checklist ensures all user-facing text is properly managed through Sanity CMS, enabling seamless multilingual support while maintaining the existing clean architecture.*